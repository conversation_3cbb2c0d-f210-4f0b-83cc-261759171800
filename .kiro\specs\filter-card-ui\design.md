# Design Document

## Overview

The Filter Card UI feature will transform the current horizontal scrollable filter pills into an immersive, card-based onboarding and customization experience. This design leverages the existing Jobbify design system with modern gradients, animations, and the established dark theme aesthetic to create an engaging step-by-step filter setup process.

## Architecture

### Component Structure
```
FilterCardFlow/
├── FilterOnboardingFlow.tsx      # Main container component
├── FilterCard.tsx                # Individual filter card component
├── ProgressIndicator.tsx         # Progress tracking component
├── FilterSummary.tsx             # Final summary screen
└── components/
    ├── LocationFilter.tsx        # Location-specific filter card
    ├── JobTypeFilter.tsx         # Job type selection card
    ├── SalaryFilter.tsx          # Salary range card
    ├── ExperienceFilter.tsx      # Experience level card
    └── CompanySizeFilter.tsx     # Company size preferences card
```

### Navigation Flow
```
Welcome Screen → Filter Card Flow → Main App
                      ↓
    Location → Job Type → Salary → Experience → Company Size → Summary
```

## Components and Interfaces

### FilterOnboardingFlow Component

**Purpose**: Main container that orchestrates the filter setup flow with smooth transitions and progress tracking.

**Key Features**:
- Animated card transitions using Moti/Reanimated
- Progress indicator with step navigation
- Gesture-based navigation (swipe to advance)
- Skip functionality with default preferences
- Integration with existing onboarding service

**Props Interface**:
```typescript
interface FilterOnboardingFlowProps {
  isOnboarding?: boolean;          // true for new users, false for editing
  existingPreferences?: UserJobPreferences;
  onComplete: (preferences: UserJobPreferences) => void;
  onSkip?: () => void;
}
```

### FilterCard Component

**Purpose**: Reusable card component that provides consistent styling and behavior for all filter types.

**Design Specifications**:
- **Dimensions**: Full screen width with 24px horizontal padding
- **Height**: 70% of screen height for optimal content display
- **Background**: Dark theme with gradient overlays matching existing patterns
- **Border Radius**: 24px for modern appearance
- **Shadow**: Elevated shadow with blur radius 20px, opacity 0.3

**Visual Elements**:
- **Header Section**: Icon, title, and description
- **Content Area**: Filter-specific interactive elements
- **Footer**: Navigation buttons (Back, Next/Complete, Skip)
- **Progress Ring**: Circular progress indicator around main action button

**Animation States**:
- **Entry**: Slide in from right with scale animation (0.9 → 1.0)
- **Exit**: Slide out to left with fade
- **Interaction**: Micro-animations for selections with spring physics
- **Completion**: Success animation with checkmark and color transition

### Individual Filter Cards

#### 1. LocationFilter Card
**Icon**: Map pin with gradient (emerald to teal)
**Content**:
- Search input for city/location with autocomplete
- Toggle for remote work preference
- Commute distance slider (0-50 miles)
- Relocation willingness toggle
- Popular locations as quick-select chips

**Interactions**:
- Real-time search with debounced API calls
- Animated slider with haptic feedback
- Toggle switches with smooth transitions

#### 2. JobTypeFilter Card
**Icon**: Briefcase with dynamic gradient (indigo to purple)
**Content**:
- Multi-select grid of job types (Full-time, Part-time, Contract, Freelance, Internship)
- Industry selection with searchable dropdown
- Work arrangement preferences (On-site, Remote, Hybrid)

**Layout**: 
- 2-column grid for job types
- Expandable industry selector
- Horizontal chips for work arrangements

#### 3. SalaryFilter Card
**Icon**: Dollar sign with gold gradient
**Content**:
- Dual-range slider for min/max salary
- Currency selector (USD, EUR, etc.)
- Salary negotiability toggle
- Benefits importance rating (Health, PTO, Equity, etc.)

**Visual Feedback**:
- Real-time salary range display
- Animated currency symbols
- Star rating system for benefits

#### 4. ExperienceFilter Card
**Icon**: Trophy with bronze-to-gold gradient
**Content**:
- Experience level selector (Entry, Junior, Mid, Senior, Lead, Executive)
- Skills input with tag system
- Years of experience slider
- Preferred role types multi-select

**Interactions**:
- Animated level progression visual
- Skill tags with auto-suggestions
- Role type cards with icons

#### 5. CompanySizeFilter Card
**Icon**: Building with scaling gradient
**Content**:
- Company size ranges (Startup <50, Small 50-200, Medium 200-1000, Large 1000+)
- Company culture preferences
- Growth stage preferences (Early, Growth, Mature)

**Layout**:
- Visual company size representations
- Culture tags with emoji icons
- Growth stage timeline visual

### ProgressIndicator Component

**Design**:
- Horizontal progress bar with step indicators
- Current step highlighted with brand colors
- Completed steps show checkmark icons
- Tappable indicators for direct navigation
- Smooth animated transitions between states

**Position**: Fixed at top of screen with safe area padding

### FilterSummary Component

**Purpose**: Final review screen showing all selected preferences with edit capabilities.

**Layout**:
- Categorized preference cards
- Edit buttons for each category
- Animated preference tags
- Completion celebration animation
- "Start Job Hunting" CTA button

## Data Models

### Enhanced UserJobPreferences Interface
```typescript
interface UserJobPreferences {
  // Existing fields...
  
  // Enhanced location preferences
  preferred_locations: LocationPreference[];
  remote_work_preference: 'required' | 'preferred' | 'acceptable' | 'not_preferred';
  max_commute_distance: number;
  willing_to_relocate: boolean;
  
  // Enhanced job type preferences
  preferred_job_types: JobType[];
  preferred_industries: string[];
  work_arrangement: 'onsite' | 'remote' | 'hybrid' | 'flexible';
  
  // Enhanced compensation
  salary_range: {
    min: number;
    max: number;
    currency: string;
    negotiable: boolean;
  };
  benefits_importance: BenefitsRating;
  
  // Enhanced experience
  experience_level: ExperienceLevel;
  skills: string[];
  years_experience: number;
  preferred_roles: string[];
  
  // Enhanced company preferences
  preferred_company_sizes: CompanySize[];
  company_culture_preferences: string[];
  growth_stage_preferences: GrowthStage[];
  
  // Metadata
  setup_completed_at: string;
  last_updated_at: string;
}

interface LocationPreference {
  city: string;
  state?: string;
  country: string;
  priority: number; // 1-5 ranking
}

interface BenefitsRating {
  health_insurance: number; // 1-5 importance
  paid_time_off: number;
  equity: number;
  retirement: number;
  professional_development: number;
}
```

## Error Handling

### Validation Strategy
- **Real-time validation**: Immediate feedback on invalid inputs
- **Progressive validation**: Validate each card before allowing advancement
- **Graceful degradation**: Allow skipping with warnings for incomplete data
- **Offline support**: Cache selections locally, sync when online

### Error States
- **Network errors**: Retry mechanisms with exponential backoff
- **Validation errors**: Inline error messages with correction guidance
- **Save failures**: Local persistence with background retry
- **Navigation errors**: Fallback to previous valid state

### User Feedback
- **Loading states**: Skeleton screens and progress indicators
- **Success states**: Animated confirmations and haptic feedback
- **Error states**: Clear error messages with actionable solutions

## Testing Strategy

### Unit Testing
- **Component rendering**: Snapshot tests for all filter cards
- **State management**: Test preference updates and validation
- **Navigation logic**: Test flow progression and back navigation
- **Data persistence**: Test save/load operations

### Integration Testing
- **End-to-end flow**: Complete onboarding journey
- **API integration**: Preference saving and loading
- **Cross-platform**: iOS and Android behavior consistency
- **Accessibility**: Screen reader and keyboard navigation

### Visual Testing
- **Design consistency**: Compare against design specifications
- **Animation performance**: Frame rate and smoothness testing
- **Responsive design**: Various screen sizes and orientations
- **Theme compatibility**: Light and dark theme variations

### User Testing
- **Usability testing**: Task completion rates and user satisfaction
- **A/B testing**: Compare card flow vs. traditional form
- **Performance testing**: Load times and memory usage
- **Accessibility testing**: Users with disabilities

## Performance Considerations

### Optimization Strategies
- **Lazy loading**: Load filter cards on-demand
- **Image optimization**: Use optimized SVG icons and gradients
- **Animation performance**: Use native driver for smooth animations
- **Memory management**: Proper cleanup of event listeners and timers

### Caching Strategy
- **Preference caching**: Local storage for quick access
- **Asset caching**: Cache icons and images for offline use
- **API response caching**: Cache location and industry data
- **State persistence**: Maintain progress across app restarts

### Bundle Size Impact
- **Code splitting**: Separate filter components from main bundle
- **Tree shaking**: Remove unused dependencies
- **Asset optimization**: Compress images and optimize SVGs
- **Lazy imports**: Dynamic imports for non-critical components

## Accessibility Features

### Screen Reader Support
- **Semantic markup**: Proper heading hierarchy and labels
- **Focus management**: Logical tab order and focus indicators
- **Announcements**: Progress updates and state changes
- **Alternative text**: Descriptive labels for icons and images

### Keyboard Navigation
- **Tab navigation**: Full keyboard accessibility
- **Shortcut keys**: Quick navigation between cards
- **Focus indicators**: Clear visual focus states
- **Escape handling**: Cancel operations and return to previous state

### Visual Accessibility
- **High contrast**: Support for high contrast mode
- **Font scaling**: Respect system font size preferences
- **Color independence**: Don't rely solely on color for information
- **Motion preferences**: Respect reduced motion settings

## Integration Points

### Existing Systems
- **Onboarding Service**: Integrate with existing onboarding flow
- **Job Recommendation**: Update algorithm with new preferences
- **User Profile**: Sync with profile management system
- **Analytics**: Track user interactions and completion rates

### API Endpoints
- **Location Search**: Autocomplete for cities and locations
- **Industry Data**: List of available industries and roles
- **Preference Storage**: Save and retrieve user preferences
- **Validation**: Server-side validation of preference data

### Navigation Integration
- **Router Integration**: Deep linking to specific filter cards
- **Back Button**: Handle Android back button behavior
- **Tab Navigation**: Integrate with existing tab structure
- **Modal Presentation**: Present as modal overlay when editing

## Future Enhancements

### Advanced Features
- **AI Recommendations**: Suggest preferences based on profile
- **Smart Defaults**: Learn from user behavior patterns
- **Social Integration**: Import preferences from LinkedIn
- **Collaborative Filtering**: Suggest based on similar users

### Personalization
- **Adaptive UI**: Adjust based on user preferences and behavior
- **Custom Themes**: Allow users to customize card appearance
- **Preference Templates**: Save and share preference sets
- **Quick Setup**: One-click setup for common job types

### Analytics and Insights
- **Completion Analytics**: Track drop-off points and optimize flow
- **Preference Analytics**: Understand popular preference combinations
- **Performance Metrics**: Monitor load times and user satisfaction
- **A/B Testing Framework**: Test different card designs and flows