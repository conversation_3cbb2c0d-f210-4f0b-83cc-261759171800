import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { useAppContext } from '@/context/AppContext';
import { DarkTheme, LightTheme } from '@/constants/Theme';
import FilterOnboardingFlow from './FilterOnboardingFlow';
import { EnhancedUserJobPreferences, createDefaultEnhancedPreferences } from '@/types/filterTypes';

export default function FilterCardTest() {
  const { theme, user } = useAppContext();
  const themeColors = theme === 'light' ? LightTheme : DarkTheme;
  const [showFlow, setShowFlow] = useState(false);
  const [testMode, setTestMode] = useState<'onboarding' | 'editing'>('onboarding');

  const handleComplete = (preferences: EnhancedUserJobPreferences) => {
    console.log('Filter setup completed:', preferences);
    Alert.alert(
      'Setup Complete!',
      `Your job preferences have been saved successfully.\n\nMode: ${testMode}\nPreferences: ${Object.keys(preferences).length} fields`,
      [{ text: 'OK', onPress: () => setShowFlow(false) }]
    );
  };

  const handleSkip = () => {
    console.log('Filter setup skipped');
    Alert.alert(
      'Setup Skipped',
      'You can set up your preferences later from the settings.',
      [{ text: 'OK', onPress: () => setShowFlow(false) }]
    );
  };

  const startOnboardingFlow = () => {
    setTestMode('onboarding');
    setShowFlow(true);
  };

  const startEditingFlow = () => {
    setTestMode('editing');
    setShowFlow(true);
  };

  if (showFlow) {
    const existingPrefs = testMode === 'editing' && user 
      ? createDefaultEnhancedPreferences(user.id) 
      : undefined;

    return (
      <FilterOnboardingFlow
        isOnboarding={testMode === 'onboarding'}
        existingPreferences={existingPrefs}
        onComplete={handleComplete}
        onSkip={handleSkip}
      />
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: themeColors.background }]}>
      <StatusBar style={theme === 'light' ? 'dark' : 'light'} />
      
      <View style={styles.content}>
        <Text style={[styles.title, { color: themeColors.text }]}>
          Filter Card Test
        </Text>
        
        <Text style={[styles.description, { color: themeColors.textSecondary }]}>
          Test the FilterOnboardingFlow component with all its enhanced features:
          {'\n'}• State management for all filter preferences
          {'\n'}• Smooth navigation between filter cards
          {'\n'}• Progress tracking with step validation
          {'\n'}• Skip functionality with default preferences
          {'\n'}• Validation and comprehensive error handling
          {'\n'}• Haptic feedback and animations
          {'\n'}• Integration with existing preference system
        </Text>

        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={[styles.button, styles.primaryButton, { backgroundColor: themeColors.primary }]}
            onPress={startOnboardingFlow}
          >
            <Text style={styles.buttonText}>Test Onboarding Flow</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.button, styles.secondaryButton, { borderColor: themeColors.border }]}
            onPress={startEditingFlow}
          >
            <Text style={[styles.buttonText, styles.secondaryButtonText, { color: themeColors.text }]}>
              Test Editing Flow
            </Text>
          </TouchableOpacity>
        </View>

        {user && (
          <Text style={[styles.userInfo, { color: themeColors.textSecondary }]}>
            Logged in as: {user.email}
          </Text>
        )}

        <View style={styles.featureList}>
          <Text style={[styles.featureTitle, { color: themeColors.text }]}>
            Implemented Features:
          </Text>
          <Text style={[styles.featureItem, { color: themeColors.textSecondary }]}>
            ✅ Main flow container with state management
          </Text>
          <Text style={[styles.featureItem, { color: themeColors.textSecondary }]}>
            ✅ Navigation logic with smooth transitions
          </Text>
          <Text style={[styles.featureItem, { color: themeColors.textSecondary }]}>
            ✅ Progress tracking and step validation
          </Text>
          <Text style={[styles.featureItem, { color: themeColors.textSecondary }]}>
            ✅ Skip functionality with default handling
          </Text>
          <Text style={[styles.featureItem, { color: themeColors.textSecondary }]}>
            ✅ Enhanced error handling and persistence
          </Text>
          <Text style={[styles.featureItem, { color: themeColors.textSecondary }]}>
            ✅ Integration with existing services
          </Text>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: '800',
    marginBottom: 16,
    textAlign: 'center',
  },
  description: {
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 32,
    textAlign: 'center',
    maxWidth: 360,
  },
  buttonContainer: {
    width: '100%',
    maxWidth: 300,
    gap: 16,
    marginBottom: 24,
  },
  button: {
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 16,
    alignItems: 'center',
  },
  primaryButton: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
  },
  secondaryButton: {
    borderWidth: 1.5,
    backgroundColor: 'transparent',
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '700',
    color: '#FFFFFF',
    textAlign: 'center',
  },
  secondaryButtonText: {
    fontWeight: '600',
  },
  userInfo: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 24,
  },
  featureList: {
    alignItems: 'flex-start',
    maxWidth: 360,
  },
  featureTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 12,
  },
  featureItem: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 4,
  },
});