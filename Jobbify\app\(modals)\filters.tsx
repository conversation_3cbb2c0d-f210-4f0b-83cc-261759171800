import React, { useState, useCallback, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Animated,
  Platform,
  ActivityIndicator,
  Alert,
  SafeAreaView,
  Dimensions,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { Stack, router } from 'expo-router';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { MotiView } from 'moti';
import * as Haptics from 'expo-haptics';

import { useAppContext } from '@/context/AppContext';
import { DarkTheme, LightTheme } from '@/constants/Theme';
import { LocationFilter } from '@/components/FilterCard';
import {
  EnhancedUserJobPreferences,
  createDefaultEnhancedPreferences,
  FilterCardType,
} from '@/types/filterTypes';
import { getUserJobPreferences, saveUserJobPreferences } from '@/services/jobRecommendationService';
import { markOnboardingComplete } from '@/services/onboardingService';

const { width: screenWidth } = Dimensions.get('window');

// Filter card configurations
const FILTER_CARDS: Array<{
  type: FilterCardType;
  title: string;
  description: string;
  icon: string;
  gradientColors: [string, string];
}> = [
  {
    type: 'location',
    title: 'Location',
    description: 'Set your preferred work locations and remote preferences',
    icon: 'map-marker-multiple',
    gradientColors: ['#10B981', '#059669'],
  },
  {
    type: 'job-type',
    title: 'Job Type',
    description: 'Choose your preferred employment types and work arrangements',
    icon: 'briefcase',
    gradientColors: ['#3B82F6', '#2563EB'],
  },
  {
    type: 'salary',
    title: 'Salary Range',
    description: 'Set your desired compensation and benefits preferences',
    icon: 'currency-usd',
    gradientColors: ['#F59E0B', '#D97706'],
  },
  {
    type: 'experience',
    title: 'Experience Level',
    description: 'Define your experience level and skill requirements',
    icon: 'school',
    gradientColors: ['#8B5CF6', '#7C3AED'],
  },
  {
    type: 'company-size',
    title: 'Company Size',
    description: 'Select your preferred company sizes and culture',
    icon: 'office-building',
    gradientColors: ['#EF4444', '#DC2626'],
  },
];

export default function FiltersScreen() {
  const { theme, user, refreshUserProfile } = useAppContext();
  const themeColors = theme === 'light' ? LightTheme : DarkTheme;

  // Determine if this is onboarding flow
  const isOnboarding = !user?.onboardingCompleted;

  // State management
  const [preferences, setPreferences] = useState<EnhancedUserJobPreferences | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [expandedCard, setExpandedCard] = useState<FilterCardType | null>(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Load existing preferences
  useEffect(() => {
    loadPreferences();
  }, [user]);

  const loadPreferences = useCallback(async () => {
    if (!user?.id) {
      console.warn('No user ID available for loading preferences');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      const existingPrefs = await getUserJobPreferences(user.id);
      
      if (existingPrefs) {
        // Convert basic preferences to enhanced format if needed
        const enhanced: EnhancedUserJobPreferences = {
          user_id: user.id,
          preferred_locations: existingPrefs.preferred_locations || [],
          remote_work_preference: existingPrefs.remote_work_preference || 'preferred',
          max_commute_distance: existingPrefs.max_commute_distance || 50,
          willing_to_relocate: existingPrefs.willing_to_relocate || false,
          preferred_job_types: existingPrefs.preferred_job_types || ['full-time'],
          preferred_industries: existingPrefs.preferred_industries || [],
          work_arrangement: 'flexible',
          salary_range: {
            min: existingPrefs.min_salary || 50000,
            max: existingPrefs.max_salary || 100000,
            currency: 'USD',
            negotiable: true,
          },
          benefits_importance: {
            health_insurance: 4,
            paid_time_off: 3,
            equity: 2,
            retirement: 3,
            professional_development: 4,
          },
          experience_level: 'mid',
          skills: existingPrefs.skills || [],
          years_experience: 3,
          preferred_roles: [],
          preferred_company_sizes: ['medium'],
          company_culture_preferences: [],
          growth_stage_preferences: ['growth'],
          location_weight: 0.3,
          salary_weight: 0.3,
          role_weight: 0.25,
          company_weight: 0.15,
          auto_learn_from_swipes: true,
          last_updated_at: new Date().toISOString(),
        };
        setPreferences(enhanced);
      } else {
        // Create default preferences
        setPreferences(createDefaultEnhancedPreferences(user.id));
      }
    } catch (error) {
      console.error('Error loading preferences:', error);
      Alert.alert('Error', 'Failed to load preferences. Please try again.');
      setPreferences(createDefaultEnhancedPreferences(user.id));
    } finally {
      setLoading(false);
    }
  }, [user]);

  const handlePreferencesChange = useCallback((updates: Partial<EnhancedUserJobPreferences>) => {
    if (!preferences) return;

    setPreferences(prev => ({
      ...prev!,
      ...updates,
      last_updated_at: new Date().toISOString(),
    }));
    setHasUnsavedChanges(true);
  }, [preferences]);

  const handleSavePreferences = useCallback(async () => {
    if (!user?.id) return;

    try {
      setSaving(true);

      // For onboarding, just save minimal default preferences to get user through the flow
      const basicPrefs = {
        user_id: user.id,
        preferred_locations: ['Remote'],
        remote_work_preference: 'preferred' as const,
        max_commute_distance: 50,
        willing_to_relocate: false,
        preferred_job_types: ['Full-time'],
        preferred_industries: ['Technology'],
        preferred_company_sizes: ['Medium'],
        experience_level: 'mid' as const,
        preferred_roles: ['Software Engineer'],
        min_salary: 50000,
        max_salary: 100000,
        salary_currency: 'USD',
        salary_negotiable: true,
        preferred_schedule: 'flexible' as const,
        preferred_job_titles: ['Software Engineer'],
        required_skills: ['JavaScript'],
        preferred_skills: ['React', 'TypeScript'],
        location_weight: 0.25,
        salary_weight: 0.30,
        role_weight: 0.25,
        company_weight: 0.20,
        auto_learn_from_swipes: true,
      };

      const success = await saveUserJobPreferences(basicPrefs);

      if (success) {
        setHasUnsavedChanges(false);

        // Mark onboarding as complete if this is onboarding flow
        if (isOnboarding) {
          const onboardingSuccess = await markOnboardingComplete(user.id);
          if (onboardingSuccess) {
            // Refresh user profile to update onboardingCompleted status
            await refreshUserProfile();
            Alert.alert('Welcome!', 'Your job preferences have been saved. Welcome to Jobbify!', [
              { text: 'Get Started', onPress: () => router.replace('/(tabs)') }
            ]);
          } else {
            Alert.alert('Success', 'Your preferences have been saved!');
          }
        } else {
          Alert.alert('Success', 'Your preferences have been saved!');
        }
      } else {
        throw new Error('Failed to save preferences');
      }
    } catch (error) {
      console.error('Error saving preferences:', error);
      Alert.alert('Error', 'Failed to save preferences. Please try again.');
    } finally {
      setSaving(false);
    }
  }, [preferences, user]);

  const handleCardPress = useCallback((cardType: FilterCardType) => {
    setExpandedCard(expandedCard === cardType ? null : cardType);
  }, [expandedCard]);

  const renderFilterCard = useCallback((cardConfig: typeof FILTER_CARDS[0]) => {
    const isExpanded = expandedCard === cardConfig.type;
    
    return (
      <View key={cardConfig.type} style={styles.cardWrapper}>
        <TouchableOpacity
          style={[
            styles.filterCard,
            {
              backgroundColor: themeColors.card,
              borderColor: themeColors.border,
            },
          ]}
          onPress={() => handleCardPress(cardConfig.type)}
          activeOpacity={0.7}
        >
          <View style={styles.cardHeader}>
            <View style={[styles.iconContainer, { backgroundColor: cardConfig.gradientColors[0] }]}>
              <MaterialCommunityIcons
                name={cardConfig.icon as any}
                size={24}
                color="#FFFFFF"
              />
            </View>
            <View style={styles.cardInfo}>
              <Text style={[styles.cardTitle, { color: themeColors.text }]}>
                {cardConfig.title}
              </Text>
              <Text style={[styles.cardDescription, { color: themeColors.textSecondary }]}>
                {cardConfig.description}
              </Text>
            </View>
            <MaterialCommunityIcons
              name={isExpanded ? 'chevron-up' : 'chevron-right'}
              size={24}
              color={themeColors.textSecondary}
            />
          </View>
        </TouchableOpacity>

        {/* Expanded content */}
        {isExpanded && preferences && (
          <View style={[styles.expandedContent, { backgroundColor: themeColors.background }]}>
            {cardConfig.type === 'location' && (
              <LocationFilter
                preferences={preferences}
                onPreferencesChange={handlePreferencesChange}
                onNext={() => setExpandedCard(null)}
                onBack={() => setExpandedCard(null)}
                onSkip={() => setExpandedCard(null)}
                currentStep={1}
                totalSteps={1}
              />
            )}
            {/* Other filter components will be implemented in subsequent tasks */}
            {cardConfig.type !== 'location' && (
              <View style={styles.placeholderContent}>
                <Text style={[styles.placeholderText, { color: themeColors.textSecondary }]}>
                  {cardConfig.title} filter coming soon...
                </Text>
              </View>
            )}
          </View>
        )}
      </View>
    );
  }, [expandedCard, preferences, themeColors, handleCardPress, handlePreferencesChange]);

  const handleClose = useCallback(() => {
    if (isOnboarding) {
      // During onboarding, require users to set preferences
      Alert.alert(
        'Complete Setup',
        'Please set your job preferences to continue using the app.',
        [
          { text: 'OK', style: 'default' },
        ]
      );
      return;
    }

    if (hasUnsavedChanges) {
      Alert.alert(
        'Unsaved Changes',
        'You have unsaved changes. Do you want to save them before leaving?',
        [
          { text: 'Discard', style: 'destructive', onPress: () => router.back() },
          { text: 'Cancel', style: 'cancel' },
          { text: 'Save', onPress: () => handleSavePreferences().then(() => router.back()) },
        ]
      );
    } else {
      router.back();
    }
  }, [isOnboarding, hasUnsavedChanges, handleSavePreferences]);

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: themeColors.background }]}>
        <StatusBar style={theme === 'light' ? 'dark' : 'light'} />
        <View style={styles.loadingContainer}>
          <Text style={[styles.loadingText, { color: themeColors.text }]}>
            Loading preferences...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: themeColors.background }]}>
      <StatusBar style={theme === 'light' ? 'dark' : 'light'} />
      
      {/* Header */}
      <View style={[styles.header, { borderBottomColor: themeColors.border }]}>
        <TouchableOpacity onPress={handleClose} style={styles.closeButton}>
          <MaterialCommunityIcons name="close" size={24} color={themeColors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: themeColors.text }]}>
          {isOnboarding ? 'Set Your Preferences' : 'Job Filters'}
        </Text>
        <TouchableOpacity
          onPress={handleSavePreferences}
          disabled={!hasUnsavedChanges || saving}
          style={[
            styles.saveButton,
            {
              backgroundColor: hasUnsavedChanges ? themeColors.primary : themeColors.border,
            },
          ]}
        >
          <Text
            style={[
              styles.saveButtonText,
              {
                color: hasUnsavedChanges ? '#FFFFFF' : themeColors.textSecondary,
              },
            ]}
          >
            {saving ? 'Saving...' : (isOnboarding ? 'Continue' : 'Save')}
          </Text>
        </TouchableOpacity>
      </View>

      {/* Filter Cards */}
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <Text style={[styles.subtitle, { color: themeColors.textSecondary }]}>
          Customize your job search preferences by expanding each filter below.
        </Text>
        
        {FILTER_CARDS.map(renderFilterCard)}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    fontWeight: '500',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  closeButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    flex: 1,
    textAlign: 'center',
    marginHorizontal: 16,
  },
  saveButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  saveButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 20,
  },
  subtitle: {
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 24,
    textAlign: 'center',
  },
  cardWrapper: {
    marginBottom: 16,
  },
  filterCard: {
    borderRadius: 16,
    borderWidth: 1,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  cardInfo: {
    flex: 1,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 4,
    letterSpacing: -0.3,
  },
  cardDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  expandedContent: {
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.1)',
    padding: 20,
  },
  placeholderContent: {
    padding: 40,
    alignItems: 'center',
  },
  placeholderText: {
    fontSize: 16,
    fontStyle: 'italic',
  },
});