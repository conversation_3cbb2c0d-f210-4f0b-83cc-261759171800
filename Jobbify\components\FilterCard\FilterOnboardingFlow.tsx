import React, { useState, useRef, useEffect, useCallback } from 'react';
import {
  View,
  StyleSheet,
  Animated,
  Dimensions,
  BackHandler,
  Alert,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { router } from 'expo-router';
// import * as Haptics from 'expo-haptics'; // Will be added when available
import { useAppContext } from '@/context/AppContext';
import { DarkTheme, LightTheme } from '@/constants/Theme';
import {
  FilterOnboardingFlowProps,
  EnhancedUserJobPreferences,
  FilterCardType,
  ProgressStep,
  createDefaultEnhancedPreferences,
} from '@/types/filterTypes';
import { saveUserJobPreferences } from '@/services/jobRecommendationService';
import { markOnboardingComplete } from '@/services/onboardingService';
import ProgressIndicator from './ProgressIndicator';

const { width: screenWidth } = Dimensions.get('window');

export default function FilterOnboardingFlow({
  isOnboarding = true,
  existingPreferences,
  onComplete,
  onSkip,
}: FilterOnboardingFlowProps) {
  const { theme, user } = useAppContext();
  const themeColors = theme === 'light' ? LightTheme : DarkTheme;

  // Initialize preferences with proper user validation
  const [preferences, setPreferences] = useState<EnhancedUserJobPreferences>(() => {
    if (existingPreferences) {
      return existingPreferences;
    }
    if (!user?.id) {
      console.warn('FilterOnboardingFlow: No user ID available, using empty string');
    }
    return createDefaultEnhancedPreferences(user?.id || '');
  });

  // Filter card flow configuration
  const filterCards: FilterCardType[] = [
    'location',
    'job-type', 
    'salary',
    'experience',
    'company-size'
  ];

  // State management
  const [currentCardIndex, setCurrentCardIndex] = useState(0);
  const [completedCards, setCompletedCards] = useState<Set<number>>(new Set());
  const [isValidCard, setIsValidCard] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Animation refs
  const slideAnim = useRef(new Animated.Value(0)).current;
  const fadeAnim = useRef(new Animated.Value(1)).current;

  // Progress steps configuration
  const progressSteps: ProgressStep[] = filterCards.map((cardType, index) => ({
    id: cardType,
    title: getCardTitle(cardType),
    icon: getCardIcon(cardType),
    completed: completedCards.has(index),
    active: index === currentCardIndex,
  }));

  // Handle Android back button and navigation
  useEffect(() => {
    const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
      if (currentCardIndex > 0) {
        handleBack();
        return true;
      } else if (hasUnsavedChanges) {
        handleExitConfirmation();
        return true;
      }
      return false;
    });

    return () => backHandler.remove();
  }, [currentCardIndex, hasUnsavedChanges]);

  // Preference change handler with validation
  const handlePreferencesChange = useCallback((newPreferences: Partial<EnhancedUserJobPreferences>) => {
    setPreferences(prev => ({
      ...prev,
      ...newPreferences,
      last_updated_at: new Date().toISOString(),
    }));
    setHasUnsavedChanges(true);
    
    // Validate the current card after changes
    validateCurrentCard(newPreferences);
  }, [currentCardIndex]);

  // Card validation logic
  const validateCurrentCard = useCallback((updatedPreferences?: Partial<EnhancedUserJobPreferences>) => {
    const currentPrefs = updatedPreferences ? { ...preferences, ...updatedPreferences } : preferences;
    const currentCardType = filterCards[currentCardIndex];
    
    let isValid = true;
    
    switch (currentCardType) {
      case 'location':
        // Location is valid if user has at least one preference set
        isValid = currentPrefs.preferred_locations.length > 0 || 
                 currentPrefs.remote_work_preference !== 'not_preferred';
        break;
      case 'job-type':
        // Job type is valid if at least one job type is selected
        isValid = currentPrefs.preferred_job_types.length > 0;
        break;
      case 'salary':
        // Salary is valid if range is set and min <= max
        isValid = currentPrefs.salary_range.min > 0 && 
                 currentPrefs.salary_range.max >= currentPrefs.salary_range.min;
        break;
      case 'experience':
        // Experience is valid if level is set
        isValid = !!currentPrefs.experience_level;
        break;
      case 'company-size':
        // Company size is valid if at least one size is selected
        isValid = currentPrefs.preferred_company_sizes.length > 0;
        break;
      default:
        isValid = true;
    }
    
    setIsValidCard(isValid);
    return isValid;
  }, [preferences, currentCardIndex, filterCards]);

  // Navigation handlers with validation and haptic feedback
  const handleNext = useCallback(async () => {
    // Validate current card before proceeding
    if (!validateCurrentCard()) {
      // Provide haptic feedback for invalid state
      // if (Platform.OS === 'ios') {
      //   Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      // }
      return;
    }

    // Provide success haptic feedback
    // if (Platform.OS === 'ios') {
    //   Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    // }

    // Mark current card as completed
    setCompletedCards(prev => new Set([...prev, currentCardIndex]));

    if (currentCardIndex < filterCards.length - 1) {
      // Move to next card
      animateToCard(currentCardIndex + 1);
    } else {
      // Complete the flow
      await handleComplete();
    }
  }, [currentCardIndex, filterCards.length, validateCurrentCard]);

  const handleBack = useCallback(() => {
    if (currentCardIndex > 0) {
      // Provide haptic feedback
      // if (Platform.OS === 'ios') {
      //   Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      // }
      animateToCard(currentCardIndex - 1);
    }
  }, [currentCardIndex]);

  const handleSkip = useCallback(async () => {
    // Provide haptic feedback
    // if (Platform.OS === 'ios') {
    //   Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    // }

    if (onSkip) {
      onSkip();
    } else {
      // Apply default preferences for current card and move to next
      applyDefaultPreferencesForCard(filterCards[currentCardIndex]);
      await handleNext();
    }
  }, [currentCardIndex, filterCards, onSkip, handleNext]);

  const handleStepPress = useCallback((stepIndex: number) => {
    // Allow navigation to completed steps, current step, or next step if current is completed
    const canNavigate = stepIndex <= currentCardIndex || 
                       completedCards.has(stepIndex) || 
                       (stepIndex === currentCardIndex + 1 && completedCards.has(currentCardIndex));
    
    if (canNavigate) {
      // Provide haptic feedback
      // if (Platform.OS === 'ios') {
      //   Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      // }
      animateToCard(stepIndex);
    }
  }, [currentCardIndex, completedCards]);

  // Apply default preferences for a specific card when skipped
  const applyDefaultPreferencesForCard = useCallback((cardType: FilterCardType) => {
    const defaults: Partial<EnhancedUserJobPreferences> = {};
    
    switch (cardType) {
      case 'location':
        defaults.remote_work_preference = 'preferred';
        defaults.max_commute_distance = 50;
        defaults.willing_to_relocate = false;
        break;
      case 'job-type':
        defaults.preferred_job_types = ['full-time'];
        defaults.work_arrangement = 'flexible';
        break;
      case 'salary':
        defaults.salary_range = {
          min: 50000,
          max: 100000,
          currency: 'USD',
          negotiable: true,
        };
        break;
      case 'experience':
        defaults.experience_level = 'mid';
        defaults.years_experience = 3;
        break;
      case 'company-size':
        defaults.preferred_company_sizes = ['small', 'medium'];
        break;
    }
    
    handlePreferencesChange(defaults);
  }, [handlePreferencesChange]);

  // Complete flow with proper error handling and persistence
  const handleComplete = useCallback(async () => {
    if (!user?.id) {
      Alert.alert('Error', 'User not found. Please try logging in again.');
      return;
    }

    setIsSaving(true);
    
    try {
      const finalPreferences: EnhancedUserJobPreferences = {
        ...preferences,
        user_id: user.id,
        setup_completed_at: new Date().toISOString(),
        last_updated_at: new Date().toISOString(),
      };

      // Convert enhanced preferences to basic format for existing API
      const basicPreferences = convertToBasicPreferences(finalPreferences);
      
      // Save preferences to database
      const saveSuccess = await saveUserJobPreferences(basicPreferences);
      
      if (!saveSuccess) {
        throw new Error('Failed to save preferences');
      }

      // Mark onboarding as complete if this is onboarding flow
      if (isOnboarding) {
        await markOnboardingComplete(user.id);
      }

      // Provide success haptic feedback
      // if (Platform.OS === 'ios') {
      //   Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      // }

      setHasUnsavedChanges(false);
      
      // Call completion callback
      onComplete(finalPreferences);
      
      // Navigate to main app if this is onboarding
      if (isOnboarding) {
        router.replace('/(tabs)');
      }
      
    } catch (error) {
      console.error('Error completing filter setup:', error);
      
      // Provide error haptic feedback
      // if (Platform.OS === 'ios') {
      //   Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      // }
      
      Alert.alert(
        'Error Saving Preferences',
        'There was an issue saving your preferences. Please try again.',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Retry', onPress: handleComplete },
        ]
      );
    } finally {
      setIsSaving(false);
    }
  }, [preferences, user, isOnboarding, onComplete]);

  // Handle exit confirmation when there are unsaved changes
  const handleExitConfirmation = useCallback(() => {
    if (!hasUnsavedChanges) {
      router.back();
      return;
    }

    Alert.alert(
      'Unsaved Changes',
      'You have unsaved changes. Are you sure you want to exit?',
      [
        { text: 'Stay', style: 'cancel' },
        { 
          text: 'Exit', 
          style: 'destructive',
          onPress: () => {
            setHasUnsavedChanges(false);
            router.back();
          }
        },
      ]
    );
  }, [hasUnsavedChanges]);

  // Convert enhanced preferences to basic format for existing API
  const convertToBasicPreferences = useCallback((enhanced: EnhancedUserJobPreferences) => {
    return {
      user_id: enhanced.user_id,
      preferred_locations: enhanced.preferred_locations.map(loc => loc.city),
      max_commute_distance: enhanced.max_commute_distance,
      remote_work_preference: enhanced.remote_work_preference,
      willing_to_relocate: enhanced.willing_to_relocate,
      preferred_job_types: enhanced.preferred_job_types,
      preferred_industries: enhanced.preferred_industries,
      preferred_company_sizes: enhanced.preferred_company_sizes,
      experience_level: enhanced.experience_level,
      preferred_roles: enhanced.preferred_roles,
      min_salary: enhanced.salary_range.min,
      max_salary: enhanced.salary_range.max,
      salary_currency: enhanced.salary_range.currency,
      salary_negotiable: enhanced.salary_range.negotiable,
      preferred_schedule: 'flexible' as 'flexible' | 'standard' | 'shift_work' | 'weekends_ok',
      location_weight: enhanced.location_weight,
      salary_weight: enhanced.salary_weight,
      role_weight: enhanced.role_weight,
      company_weight: enhanced.company_weight,
      auto_learn_from_swipes: enhanced.auto_learn_from_swipes,
    };
  }, []);

  // Enhanced animation with smooth transitions
  const animateToCard = useCallback((targetIndex: number) => {
    if (targetIndex === currentCardIndex) return;
    
    const direction = targetIndex > currentCardIndex ? 1 : -1;
    
    // Slide out current card with improved easing
    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: -direction * screenWidth,
        duration: 250,
        useNativeDriver: true,
      }),
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 150,
        useNativeDriver: true,
      }),
    ]).start(() => {
      // Update card index and reset validation
      setCurrentCardIndex(targetIndex);
      setIsValidCard(true);
      
      // Reset animations for new card
      slideAnim.setValue(direction * screenWidth);
      fadeAnim.setValue(0);
      
      // Slide in new card with spring animation
      Animated.parallel([
        Animated.spring(slideAnim, {
          toValue: 0,
          tension: 120,
          friction: 8,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 250,
          useNativeDriver: true,
        }),
      ]).start(() => {
        // Validate the new card after animation completes
        validateCurrentCard();
      });
    });
  }, [currentCardIndex, screenWidth, validateCurrentCard]);

  // Render current filter card with proper props
  const renderCurrentCard = useCallback(() => {
    const currentCardType = filterCards[currentCardIndex];
    
    // For now, return a placeholder - individual filter cards will be implemented in subsequent tasks
    // This placeholder includes all the props that will be passed to actual filter cards
    return (
      <View style={styles.cardPlaceholder}>
        <View style={[styles.placeholderContent, { backgroundColor: themeColors.card }]}>
          {/* Placeholder content - will be replaced with actual filter cards */}
          {/* The actual filter cards will receive these props:
            - type: currentCardType
            - preferences: preferences
            - onPreferencesChange: handlePreferencesChange
            - onNext: handleNext
            - onBack: handleBack
            - onSkip: handleSkip
            - isFirst: currentCardIndex === 0
            - isLast: currentCardIndex === filterCards.length - 1
            - currentStep: currentCardIndex + 1
            - totalSteps: filterCards.length
            - isValid: isValidCard
            - isSaving: isSaving
          */}
        </View>
      </View>
    );
  }, [
    currentCardIndex, 
    filterCards, 
    preferences, 
    handlePreferencesChange, 
    handleNext, 
    handleBack, 
    handleSkip, 
    isValidCard, 
    isSaving, 
    themeColors.card
  ]);

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: themeColors.background }]}>
      <StatusBar style={theme === 'light' ? 'dark' : 'light'} />
      
      {/* Progress Indicator */}
      <ProgressIndicator
        steps={progressSteps}
        currentStep={currentCardIndex}
        onStepPress={handleStepPress}
      />

      {/* Card Container with enhanced animations */}
      <Animated.View
        style={[
          styles.cardContainer,
          {
            transform: [{ translateX: slideAnim }],
            opacity: fadeAnim,
          },
        ]}
      >
        {renderCurrentCard()}
      </Animated.View>

      {/* Loading overlay when saving */}
      {isSaving && (
        <View style={[styles.loadingOverlay, { backgroundColor: `${themeColors.background}CC` }]}>
          <View style={[styles.loadingContent, { backgroundColor: themeColors.card }]}>
            {/* Loading indicator will be added when implementing individual cards */}
          </View>
        </View>
      )}
    </SafeAreaView>
  );
}

// Helper functions
function getCardTitle(cardType: FilterCardType): string {
  const titles: Record<FilterCardType, string> = {
    location: 'Location',
    'job-type': 'Job Type',
    salary: 'Salary',
    experience: 'Experience',
    'company-size': 'Company',
  };
  return titles[cardType];
}

function getCardIcon(cardType: FilterCardType): string {
  const icons: Record<FilterCardType, string> = {
    location: 'map-marker',
    'job-type': 'briefcase',
    salary: 'currency-usd',
    experience: 'school',
    'company-size': 'office-building',
  };
  return icons[cardType];
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  cardContainer: {
    flex: 1,
    paddingHorizontal: 20,
  },
  cardPlaceholder: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  placeholderContent: {
    width: '100%',
    height: 400,
    borderRadius: 24,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.1,
    shadowRadius: 20,
    elevation: 8,
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  loadingContent: {
    padding: 24,
    borderRadius: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 8,
  },
});