import React, { useState, useCallback, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  FlatList,
  Animated,
  Platform,
  Dimensions,
  Switch,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { <PERSON><PERSON>View, MotiText } from 'moti';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import Slider from '@react-native-community/slider';
import * as Haptics from 'expo-haptics';
import { useAppContext } from '@/context/AppContext';
import { DarkTheme, LightTheme } from '@/constants/Theme';
import FilterCard from './FilterCard';
import {
  FilterCardProps,
  LocationPreference,
  EnhancedUserJobPreferences,
} from '@/types/filterTypes';

const { width: screenWidth } = Dimensions.get('window');

// Popular locations for quick selection
const POPULAR_LOCATIONS: LocationPreference[] = [
  { city: 'New York', state: 'NY', country: 'USA', priority: 1 },
  { city: 'San Francisco', state: 'CA', country: 'USA', priority: 1 },
  { city: 'Los Angeles', state: 'CA', country: 'USA', priority: 1 },
  { city: 'Chicago', state: 'IL', country: 'USA', priority: 1 },
  { city: 'Austin', state: 'TX', country: 'USA', priority: 1 },
  { city: 'Seattle', state: 'WA', country: 'USA', priority: 1 },
  { city: 'Boston', state: 'MA', country: 'USA', priority: 1 },
  { city: 'Denver', state: 'CO', country: 'USA', priority: 1 },
];

// Mock location search function (in a real app, this would call a geocoding API)
const searchLocations = async (query: string): Promise<LocationPreference[]> => {
  if (!query || query.length < 2) return [];
  
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 300));
  
  // Filter popular locations based on query
  return POPULAR_LOCATIONS.filter(location =>
    location.city.toLowerCase().includes(query.toLowerCase()) ||
    location.state?.toLowerCase().includes(query.toLowerCase())
  );
};

interface LocationFilterProps extends Omit<FilterCardProps, 'type'> {}

export default function LocationFilter({
  preferences,
  onPreferencesChange,
  onNext,
  onBack,
  onSkip,
  isFirst,
  isLast,
  currentStep,
  totalSteps,
}: LocationFilterProps) {
  const { theme } = useAppContext();
  const themeColors = theme === 'light' ? LightTheme : DarkTheme;
  
  // Local state
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<LocationPreference[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [showSearchResults, setShowSearchResults] = useState(false);
  
  // Animation refs
  const searchResultsAnim = useRef(new Animated.Value(0)).current;
  const sliderAnim = useRef(new Animated.Value(0)).current;
  const chipAnimations = useRef(POPULAR_LOCATIONS.map(() => new Animated.Value(0))).current;
  
  // Debounced search
  const searchTimeoutRef = useRef<NodeJS.Timeout>();
  
  const handleSearch = useCallback(async (query: string) => {
    setSearchQuery(query);
    
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }
    
    if (!query || query.length < 2) {
      setSearchResults([]);
      setShowSearchResults(false);
      Animated.timing(searchResultsAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }).start();
      return;
    }
    
    searchTimeoutRef.current = setTimeout(async () => {
      setIsSearching(true);
      try {
        const results = await searchLocations(query);
        setSearchResults(results);
        setShowSearchResults(true);
        
        Animated.timing(searchResultsAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }).start();
      } catch (error) {
        console.error('Location search error:', error);
      } finally {
        setIsSearching(false);
      }
    }, 300);
  }, []);
  
  const handleLocationSelect = useCallback((location: LocationPreference) => {
    // Haptic feedback
    if (Platform.OS === 'ios') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    
    const currentLocations = preferences.preferred_locations || [];
    const isAlreadySelected = currentLocations.some(
      loc => loc.city === location.city && loc.state === location.state
    );
    
    let updatedLocations: LocationPreference[];
    
    if (isAlreadySelected) {
      // Remove location
      updatedLocations = currentLocations.filter(
        loc => !(loc.city === location.city && loc.state === location.state)
      );
    } else {
      // Add location
      updatedLocations = [...currentLocations, location];
    }
    
    onPreferencesChange({
      preferred_locations: updatedLocations,
    });
    
    // Clear search
    setSearchQuery('');
    setShowSearchResults(false);
    Animated.timing(searchResultsAnim, {
      toValue: 0,
      duration: 200,
      useNativeDriver: true,
    }).start();
  }, [preferences.preferred_locations, onPreferencesChange]);
  
  const handleRemoteWorkToggle = useCallback((value: boolean) => {
    // Haptic feedback
    if (Platform.OS === 'ios') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
    
    const newPreference = value ? 'required' : 'preferred';
    onPreferencesChange({
      remote_work_preference: newPreference,
    });
  }, [onPreferencesChange]);
  
  const handleCommuteDistanceChange = useCallback((value: number) => {
    onPreferencesChange({
      max_commute_distance: Math.round(value),
    });
  }, [onPreferencesChange]);
  
  const handleCommuteDistanceComplete = useCallback(() => {
    // Haptic feedback on slider release
    if (Platform.OS === 'ios') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
  }, []);
  
  const handleRelocationToggle = useCallback((value: boolean) => {
    // Haptic feedback
    if (Platform.OS === 'ios') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
    
    onPreferencesChange({
      willing_to_relocate: value,
    });
  }, [onPreferencesChange]);
  
  const handlePopularLocationPress = useCallback((location: LocationPreference, index: number) => {
    // Animate chip
    Animated.sequence([
      Animated.timing(chipAnimations[index], {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(chipAnimations[index], {
        toValue: 0,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();
    
    handleLocationSelect(location);
  }, [handleLocationSelect, chipAnimations]);
  
  // Initialize animations
  useEffect(() => {
    Animated.timing(sliderAnim, {
      toValue: 1,
      duration: 800,
      useNativeDriver: true,
    }).start();
  }, []);
  
  const isLocationSelected = useCallback((location: LocationPreference) => {
    return (preferences.preferred_locations || []).some(
      loc => loc.city === location.city && loc.state === location.state
    );
  }, [preferences.preferred_locations]);
  
  const renderLocationChip = ({ item, index }: { item: LocationPreference; index: number }) => {
    const isSelected = isLocationSelected(item);
    const chipScale = chipAnimations[index].interpolate({
      inputRange: [0, 1],
      outputRange: [1, 0.95],
    });
    
    return (
      <Animated.View style={{ transform: [{ scale: chipScale }] }}>
        <TouchableOpacity
          style={[
            styles.locationChip,
            {
              backgroundColor: isSelected ? '#10B981' : `${themeColors.text}10`,
              borderColor: isSelected ? '#10B981' : themeColors.border,
            },
          ]}
          onPress={() => handlePopularLocationPress(item, index)}
          activeOpacity={0.7}
        >
          <MaterialCommunityIcons
            name={isSelected ? "check-circle" : "map-marker"}
            size={16}
            color={isSelected ? '#FFFFFF' : themeColors.text}
          />
          <Text
            style={[
              styles.locationChipText,
              {
                color: isSelected ? '#FFFFFF' : themeColors.text,
                fontWeight: isSelected ? '600' : '500',
              },
            ]}
          >
            {item.city}, {item.state}
          </Text>
        </TouchableOpacity>
      </Animated.View>
    );
  };
  
  const renderSearchResult = ({ item }: { item: LocationPreference }) => (
    <TouchableOpacity
      style={[styles.searchResultItem, { borderBottomColor: themeColors.border }]}
      onPress={() => handleLocationSelect(item)}
      activeOpacity={0.7}
    >
      <MaterialCommunityIcons
        name="map-marker"
        size={20}
        color={themeColors.textSecondary}
      />
      <Text style={[styles.searchResultText, { color: themeColors.text }]}>
        {item.city}, {item.state}, {item.country}
      </Text>
    </TouchableOpacity>
  );
  
  return (
    <FilterCard
      title="Location Preferences"
      description="Where would you like to work? Set your location preferences and commute distance."
      icon="map-marker-multiple"
      gradientColors={['#10B981', '#059669']}
      onNext={onNext}
      onBack={onBack}
      onSkip={onSkip}
      isFirst={isFirst}
      isLast={isLast}
      currentStep={currentStep}
      totalSteps={totalSteps}
    >
      <View style={styles.container}>
        {/* Location Search */}
        <MotiView
          from={{ opacity: 0, translateY: 20 }}
          animate={{ opacity: 1, translateY: 0 }}
          transition={{ delay: 200 }}
          style={styles.section}
        >
          <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
            Preferred Locations
          </Text>
          
          <View style={[styles.searchContainer, { backgroundColor: `${themeColors.text}05` }]}>
            <MaterialCommunityIcons
              name="magnify"
              size={20}
              color={themeColors.textSecondary}
              style={styles.searchIcon}
            />
            <TextInput
              style={[styles.searchInput, { color: themeColors.text }]}
              placeholder="Search for cities..."
              placeholderTextColor={themeColors.textSecondary}
              value={searchQuery}
              onChangeText={handleSearch}
              autoCapitalize="words"
              autoCorrect={false}
            />
            {isSearching && (
              <MaterialCommunityIcons
                name="loading"
                size={20}
                color={themeColors.textSecondary}
                style={styles.loadingIcon}
              />
            )}
          </View>
          
          {/* Search Results */}
          {showSearchResults && (
            <Animated.View
              style={[
                styles.searchResults,
                {
                  backgroundColor: themeColors.card,
                  borderColor: themeColors.border,
                  opacity: searchResultsAnim,
                  transform: [
                    {
                      translateY: searchResultsAnim.interpolate({
                        inputRange: [0, 1],
                        outputRange: [-10, 0],
                      }),
                    },
                  ],
                },
              ]}
            >
              <FlatList
                data={searchResults}
                renderItem={renderSearchResult}
                keyExtractor={(item) => `${item.city}-${item.state}`}
                showsVerticalScrollIndicator={false}
                maxHeight={200}
              />
            </Animated.View>
          )}
          
          {/* Selected Locations */}
          {preferences.preferred_locations && preferences.preferred_locations.length > 0 && (
            <View style={styles.selectedLocations}>
              <Text style={[styles.selectedTitle, { color: themeColors.textSecondary }]}>
                Selected Locations:
              </Text>
              <View style={styles.selectedLocationsList}>
                {preferences.preferred_locations.map((location, index) => (
                  <View
                    key={`${location.city}-${location.state}`}
                    style={[styles.selectedLocationChip, { backgroundColor: '#10B981' }]}
                  >
                    <Text style={styles.selectedLocationText}>
                      {location.city}, {location.state}
                    </Text>
                    <TouchableOpacity
                      onPress={() => handleLocationSelect(location)}
                      hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                    >
                      <MaterialCommunityIcons
                        name="close-circle"
                        size={18}
                        color="#FFFFFF"
                      />
                    </TouchableOpacity>
                  </View>
                ))}
              </View>
            </View>
          )}
        </MotiView>
        
        {/* Remote Work Preference */}
        <MotiView
          from={{ opacity: 0, translateY: 20 }}
          animate={{ opacity: 1, translateY: 0 }}
          transition={{ delay: 400 }}
          style={styles.section}
        >
          <View style={styles.toggleSection}>
            <View style={styles.toggleContent}>
              <MaterialCommunityIcons
                name="laptop"
                size={24}
                color="#10B981"
                style={styles.toggleIcon}
              />
              <View style={styles.toggleText}>
                <Text style={[styles.toggleTitle, { color: themeColors.text }]}>
                  Remote Work Required
                </Text>
                <Text style={[styles.toggleDescription, { color: themeColors.textSecondary }]}>
                  Only show remote-friendly positions
                </Text>
              </View>
            </View>
            <Switch
              value={preferences.remote_work_preference === 'required'}
              onValueChange={handleRemoteWorkToggle}
              trackColor={{ false: `${themeColors.text}20`, true: '#10B981' }}
              thumbColor="#FFFFFF"
              ios_backgroundColor={`${themeColors.text}20`}
            />
          </View>
        </MotiView>
        
        {/* Commute Distance Slider */}
        <Animated.View
          style={[
            styles.section,
            {
              opacity: sliderAnim,
              transform: [
                {
                  translateY: sliderAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [20, 0],
                  }),
                },
              ],
            },
          ]}
        >
          <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
            Maximum Commute Distance
          </Text>
          
          <View style={styles.sliderContainer}>
            <View style={styles.sliderHeader}>
              <Text style={[styles.sliderValue, { color: '#10B981' }]}>
                {preferences.max_commute_distance || 50} miles
              </Text>
            </View>
            
            <Slider
              style={styles.slider}
              minimumValue={0}
              maximumValue={100}
              value={preferences.max_commute_distance || 50}
              onValueChange={handleCommuteDistanceChange}
              onSlidingComplete={handleCommuteDistanceComplete}
              minimumTrackTintColor="#10B981"
              maximumTrackTintColor={`${themeColors.text}20`}
              thumbStyle={styles.sliderThumb}
              trackStyle={styles.sliderTrack}
            />
            
            <View style={styles.sliderLabels}>
              <Text style={[styles.sliderLabel, { color: themeColors.textSecondary }]}>
                0 miles
              </Text>
              <Text style={[styles.sliderLabel, { color: themeColors.textSecondary }]}>
                100+ miles
              </Text>
            </View>
          </View>
        </Animated.View>
        
        {/* Relocation Willingness */}
        <MotiView
          from={{ opacity: 0, translateY: 20 }}
          animate={{ opacity: 1, translateY: 0 }}
          transition={{ delay: 600 }}
          style={styles.section}
        >
          <View style={styles.toggleSection}>
            <View style={styles.toggleContent}>
              <MaterialCommunityIcons
                name="truck-delivery"
                size={24}
                color="#10B981"
                style={styles.toggleIcon}
              />
              <View style={styles.toggleText}>
                <Text style={[styles.toggleTitle, { color: themeColors.text }]}>
                  Open to Relocation
                </Text>
                <Text style={[styles.toggleDescription, { color: themeColors.textSecondary }]}>
                  Willing to move for the right opportunity
                </Text>
              </View>
            </View>
            <Switch
              value={preferences.willing_to_relocate || false}
              onValueChange={handleRelocationToggle}
              trackColor={{ false: `${themeColors.text}20`, true: '#10B981' }}
              thumbColor="#FFFFFF"
              ios_backgroundColor={`${themeColors.text}20`}
            />
          </View>
        </MotiView>
        
        {/* Popular Locations */}
        <MotiView
          from={{ opacity: 0, translateY: 20 }}
          animate={{ opacity: 1, translateY: 0 }}
          transition={{ delay: 800 }}
          style={styles.section}
        >
          <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
            Popular Locations
          </Text>
          
          <FlatList
            data={POPULAR_LOCATIONS}
            renderItem={renderLocationChip}
            keyExtractor={(item) => `${item.city}-${item.state}`}
            numColumns={2}
            columnWrapperStyle={styles.chipRow}
            showsVerticalScrollIndicator={false}
            scrollEnabled={false}
          />
        </MotiView>
      </View>
    </FilterCard>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 16,
    letterSpacing: -0.3,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginBottom: 8,
  },
  searchIcon: {
    marginRight: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
  },
  loadingIcon: {
    marginLeft: 8,
  },
  searchResults: {
    borderRadius: 12,
    borderWidth: 1,
    maxHeight: 200,
    marginBottom: 16,
  },
  searchResultItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  searchResultText: {
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 12,
  },
  selectedLocations: {
    marginTop: 16,
  },
  selectedTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  selectedLocationsList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  selectedLocationChip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    gap: 6,
  },
  selectedLocationText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  toggleSection: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 8,
  },
  toggleContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  toggleIcon: {
    marginRight: 16,
  },
  toggleText: {
    flex: 1,
  },
  toggleTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  toggleDescription: {
    fontSize: 14,
    opacity: 0.8,
  },
  sliderContainer: {
    paddingHorizontal: 8,
  },
  sliderHeader: {
    alignItems: 'center',
    marginBottom: 16,
  },
  sliderValue: {
    fontSize: 24,
    fontWeight: '800',
    letterSpacing: -0.5,
  },
  slider: {
    width: '100%',
    height: 40,
  },
  sliderThumb: {
    backgroundColor: '#10B981',
    width: 24,
    height: 24,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  sliderTrack: {
    height: 6,
    borderRadius: 3,
  },
  sliderLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  sliderLabel: {
    fontSize: 12,
    fontWeight: '500',
  },
  locationChip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    gap: 6,
    marginBottom: 8,
  },
  locationChipText: {
    fontSize: 14,
    fontWeight: '500',
  },
  chipRow: {
    justifyContent: 'space-between',
    paddingHorizontal: 4,
  },
});