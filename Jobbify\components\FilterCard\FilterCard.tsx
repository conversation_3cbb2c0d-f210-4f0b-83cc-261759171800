import React, { useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  Animated,
  Platform,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { MotiView } from 'moti';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useAppContext } from '@/context/AppContext';
import { DarkTheme, LightTheme } from '@/constants/Theme';
import { FilterCardProps } from '@/types/filterTypes';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface BaseFilterCardProps extends FilterCardProps {
  title: string;
  description: string;
  icon: keyof typeof MaterialCommunityIcons.glyphMap;
  gradientColors: [string, string];
  children: React.ReactNode;
}

export default function FilterCard({
  title,
  description,
  icon,
  gradientColors,
  children,
  onNext,
  onBack,
  onSkip,
  isFirst = false,
  isLast = false,
  currentStep,
  totalSteps,
  isValid = true,
  isSaving = false,
}: BaseFilterCardProps) {
  const { theme } = useAppContext();
  const themeColors = theme === 'light' ? LightTheme : DarkTheme;
  
  // Animation refs
  const scaleAnim = useRef(new Animated.Value(0.9)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  const progressAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Entry animation sequence
    Animated.parallel([
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      }),
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 400,
        useNativeDriver: true,
      }),
      Animated.spring(slideAnim, {
        toValue: 0,
        tension: 80,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();

    // Progress animation
    Animated.timing(progressAnim, {
      toValue: currentStep / totalSteps,
      duration: 600,
      useNativeDriver: false,
    }).start();
  }, [currentStep, totalSteps]);

  const handleButtonPress = (action: () => void) => {
    // Button press animation
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 0.98,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start(() => {
      action();
    });
  };

  const progressWidth = progressAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0%', '100%'],
  });

  return (
    <View style={[styles.container, { backgroundColor: themeColors.background }]}>
      {/* Background gradient */}
      <LinearGradient
        colors={[themeColors.background, `${gradientColors[0]}10`, themeColors.background]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={StyleSheet.absoluteFill}
      />

      {/* Progress indicator */}
      <View style={styles.progressContainer}>
        <View style={[styles.progressTrack, { backgroundColor: `${themeColors.text}20` }]}>
          <Animated.View
            style={[
              styles.progressFill,
              {
                width: progressWidth,
                backgroundColor: gradientColors[0],
              },
            ]}
          />
        </View>
        <Text style={[styles.progressText, { color: themeColors.textSecondary }]}>
          {currentStep} of {totalSteps}
        </Text>
      </View>

      {/* Main card */}
      <Animated.View
        style={[
          styles.cardContainer,
          {
            transform: [
              { scale: scaleAnim },
              { translateY: slideAnim },
            ],
            opacity: fadeAnim,
          },
        ]}
      >
        <MotiView
          from={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{
            type: 'spring',
            damping: 15,
            stiffness: 100,
            delay: 200,
          }}
          style={[styles.card, { backgroundColor: themeColors.card }]}
        >
          {/* Card header */}
          <View style={styles.header}>
            <View style={styles.iconContainer}>
              <LinearGradient
                colors={gradientColors}
                style={styles.iconGradient}
              >
                <MaterialCommunityIcons
                  name={icon}
                  size={32}
                  color="#FFFFFF"
                />
              </LinearGradient>
            </View>
            
            <View style={styles.headerText}>
              <Text style={[styles.title, { color: themeColors.text }]}>
                {title}
              </Text>
              <Text style={[styles.description, { color: themeColors.textSecondary }]}>
                {description}
              </Text>
            </View>
          </View>

          {/* Card content */}
          <View style={styles.content}>
            {children}
          </View>

          {/* Card footer */}
          <View style={styles.footer}>
            <View style={styles.navigationButtons}>
              {!isFirst && (
                <TouchableOpacity
                  style={[
                    styles.button,
                    styles.secondaryButton,
                    { borderColor: themeColors.border },
                  ]}
                  onPress={() => handleButtonPress(onBack)}
                  activeOpacity={0.7}
                >
                  <MaterialCommunityIcons
                    name="chevron-left"
                    size={20}
                    color={themeColors.text}
                  />
                  <Text style={[styles.buttonText, { color: themeColors.text }]}>
                    Back
                  </Text>
                </TouchableOpacity>
              )}

              <TouchableOpacity
                style={[styles.button, styles.skipButton]}
                onPress={() => handleButtonPress(onSkip)}
                activeOpacity={0.7}
              >
                <Text style={[styles.buttonText, { color: themeColors.textSecondary }]}>
                  Skip
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.button, styles.primaryButton]}
                onPress={() => handleButtonPress(onNext)}
                activeOpacity={0.7}
              >
                <LinearGradient
                  colors={gradientColors}
                  style={styles.primaryButtonGradient}
                >
                  <Text style={styles.primaryButtonText}>
                    {isLast ? 'Complete' : 'Next'}
                  </Text>
                  <MaterialCommunityIcons
                    name={isLast ? "check" : "chevron-right"}
                    size={20}
                    color="#FFFFFF"
                  />
                </LinearGradient>
              </TouchableOpacity>
            </View>
          </View>
        </MotiView>
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 60,
    paddingBottom: 40,
  },
  progressContainer: {
    marginBottom: 32,
    alignItems: 'center',
  },
  progressTrack: {
    width: '100%',
    height: 4,
    borderRadius: 2,
    marginBottom: 8,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 2,
  },
  progressText: {
    fontSize: 14,
    fontWeight: '600',
  },
  cardContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  card: {
    borderRadius: 24,
    padding: 24,
    minHeight: screenHeight * 0.7,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.1,
    shadowRadius: 20,
    elevation: 8,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 32,
  },
  iconContainer: {
    marginRight: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
  },
  iconGradient: {
    width: 64,
    height: 64,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerText: {
    flex: 1,
  },
  title: {
    fontSize: 24,
    fontWeight: '800',
    marginBottom: 4,
    letterSpacing: -0.5,
  },
  description: {
    fontSize: 16,
    lineHeight: 22,
    opacity: 0.8,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    minHeight: 200,
  },
  footer: {
    marginTop: 32,
  },
  navigationButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    gap: 12,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderRadius: 16,
    minWidth: 80,
  },
  secondaryButton: {
    borderWidth: 1.5,
    backgroundColor: 'transparent',
  },
  skipButton: {
    backgroundColor: 'transparent',
    flex: 1,
  },
  primaryButton: {
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
  },
  primaryButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    gap: 8,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 4,
  },
  primaryButtonText: {
    fontSize: 16,
    fontWeight: '700',
    color: '#FFFFFF',
  },
});