import React, { useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { <PERSON>tiView } from 'moti';
import { useAppContext } from '@/context/AppContext';
import { DarkTheme, LightTheme } from '@/constants/Theme';
import { ProgressIndicatorProps, ProgressStep } from '@/types/filterTypes';

const { width: screenWidth } = Dimensions.get('window');

export default function ProgressIndicator({
  steps,
  currentStep,
  onStepPress,
}: ProgressIndicatorProps) {
  const { theme } = useAppContext();
  const themeColors = theme === 'light' ? LightTheme : DarkTheme;
  
  const progressAnim = useRef(new Animated.Value(0)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    // Animate progress line
    Animated.timing(progressAnim, {
      toValue: currentStep / (steps.length - 1),
      duration: 600,
      useNativeDriver: false,
    }).start();

    // Pulse animation for active step
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    ).start();
  }, [currentStep, steps.length]);

  const progressWidth = progressAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0, screenWidth - 80],
  });

  const getStepIcon = (step: ProgressStep): keyof typeof MaterialCommunityIcons.glyphMap => {
    const iconMap: Record<string, keyof typeof MaterialCommunityIcons.glyphMap> = {
      location: 'map-marker-outline',
      'job-type': 'briefcase-outline',
      salary: 'currency-usd',
      experience: 'school-outline',
      'company-size': 'office-building-outline',
    };
    return iconMap[step.id] || 'circle-outline';
  };

  const getStepGradient = (stepIndex: number): [string, string] => {
    const gradients: [string, string][] = [
      ['#10B981', '#14B8A6'], // emerald to teal
      ['#6366F1', '#8B5CF6'], // indigo to purple
      ['#F59E0B', '#F97316'], // amber to orange
      ['#EF4444', '#EC4899'], // red to pink
      ['#3B82F6', '#1D4ED8'], // blue to blue-700
    ];
    return gradients[stepIndex % gradients.length];
  };

  const renderStep = (step: ProgressStep, index: number) => {
    const isActive = index === currentStep;
    const isCompleted = step.completed;
    const isPast = index < currentStep;
    const isFuture = index > currentStep;
    
    const stepGradient = getStepGradient(index);
    const stepIcon = getStepIcon(step);

    return (
      <TouchableOpacity
        key={step.id}
        style={styles.stepContainer}
        onPress={() => onStepPress(index)}
        activeOpacity={0.7}
        disabled={isFuture}
      >
        <MotiView
          from={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{
            type: 'spring',
            damping: 15,
            stiffness: 100,
            delay: index * 100,
          }}
        >
          <Animated.View
            style={[
              styles.stepCircle,
              {
                transform: isActive ? [{ scale: pulseAnim }] : [{ scale: 1 }],
              },
            ]}
          >
            {isCompleted || isPast ? (
              <LinearGradient
                colors={stepGradient}
                style={styles.stepCircleGradient}
              >
                <MaterialCommunityIcons
                  name="check"
                  size={16}
                  color="#FFFFFF"
                />
              </LinearGradient>
            ) : isActive ? (
              <LinearGradient
                colors={stepGradient}
                style={styles.stepCircleGradient}
              >
                <MaterialCommunityIcons
                  name={stepIcon}
                  size={16}
                  color="#FFFFFF"
                />
              </LinearGradient>
            ) : (
              <View
                style={[
                  styles.stepCircleInactive,
                  { 
                    backgroundColor: `${themeColors.text}20`,
                    borderColor: `${themeColors.text}30`,
                  },
                ]}
              >
                <MaterialCommunityIcons
                  name={stepIcon}
                  size={16}
                  color={`${themeColors.text}60`}
                />
              </View>
            )}
          </Animated.View>
          
          <Text
            style={[
              styles.stepLabel,
              {
                color: isActive || isCompleted || isPast 
                  ? themeColors.text 
                  : `${themeColors.text}60`,
                fontWeight: isActive ? '700' : '600',
              },
            ]}
            numberOfLines={1}
          >
            {step.title}
          </Text>
        </MotiView>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      {/* Progress line background */}
      <View
        style={[
          styles.progressLine,
          { backgroundColor: `${themeColors.text}20` },
        ]}
      />
      
      {/* Animated progress line */}
      <Animated.View
        style={[
          styles.progressLineActive,
          {
            width: progressWidth,
            backgroundColor: getStepGradient(currentStep)[0],
          },
        ]}
      />
      
      {/* Steps */}
      <View style={styles.stepsContainer}>
        {steps.map((step, index) => renderStep(step, index))}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 40,
    paddingVertical: 20,
    position: 'relative',
  },
  progressLine: {
    position: 'absolute',
    top: 44,
    left: 64,
    right: 64,
    height: 2,
    borderRadius: 1,
  },
  progressLineActive: {
    position: 'absolute',
    top: 44,
    left: 64,
    height: 2,
    borderRadius: 1,
  },
  stepsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  stepContainer: {
    alignItems: 'center',
    flex: 1,
  },
  stepCircle: {
    width: 32,
    height: 32,
    borderRadius: 16,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  stepCircleGradient: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  stepCircleInactive: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
  },
  stepLabel: {
    fontSize: 12,
    textAlign: 'center',
    maxWidth: 60,
  },
});