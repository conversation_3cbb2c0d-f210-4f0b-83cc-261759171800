/**
 * Test script to verify the authentication flow works correctly
 * This script tests the complete onboarding flow from login to preferences saving
 */

const { createClient } = require('@supabase/supabase-js');

// Supabase configuration
const supabaseUrl = 'https://ubueawlkwlvgzxcslats.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVidWVhd2xrd2x2Z3p4Y3NsYXRzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU0MzgzMTIsImV4cCI6MjA2MTAxNDMxMn0._YMssGKgq17C5XMkPnN5rq5Zhu_u4WsvNiveYIPd4lg';

const supabase = createClient(supabaseUrl, supabaseKey);

// Test credentials
const testEmail = '<EMAIL>';
const testPassword = 'Keshav2816';

async function testAuthFlow() {
  console.log('🧪 Starting authentication flow test...\n');

  try {
    // Step 1: Test login
    console.log('1️⃣ Testing login...');
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: testEmail,
      password: testPassword,
    });

    if (authError) {
      console.error('❌ Login failed:', authError.message);
      return;
    }

    console.log('✅ Login successful!');
    console.log('   User ID:', authData.user.id);
    console.log('   Email:', authData.user.email);

    // Step 2: Check profile
    console.log('\n2️⃣ Checking user profile...');
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', authData.user.id)
      .single();

    if (profileError) {
      console.error('❌ Profile fetch failed:', profileError.message);
      return;
    }

    console.log('✅ Profile found!');
    console.log('   Name:', profile.name);
    console.log('   User Type:', profile.user_type);
    console.log('   Onboarding Completed:', profile.onboarding_completed);

    // Step 3: Check existing job preferences
    console.log('\n3️⃣ Checking existing job preferences...');
    const { data: existingPrefs, error: prefsError } = await supabase
      .from('user_job_preferences')
      .select('*')
      .eq('user_id', authData.user.id);

    if (prefsError) {
      console.error('❌ Preferences fetch failed:', prefsError.message);
      return;
    }

    console.log('✅ Preferences check complete!');
    console.log('   Existing preferences count:', existingPrefs.length);

    // Step 4: Test saving preferences (simulate onboarding)
    console.log('\n4️⃣ Testing preferences save...');
    const testPreferences = {
      user_id: authData.user.id,
      preferred_locations: ['Remote'],
      remote_work_preference: 'preferred',
      max_commute_distance: 50,
      willing_to_relocate: false,
      preferred_job_types: ['Full-time'],
      preferred_industries: ['Technology'],
      preferred_company_sizes: ['Medium'],
      experience_level: 'mid',
      min_salary: 50000,
      max_salary: 100000,
      salary_currency: 'USD',
      salary_negotiable: true,
      preferred_schedule: 'flexible',
      preferred_job_titles: ['Software Engineer'],
      required_skills: ['JavaScript'],
      preferred_skills: ['React', 'TypeScript'],
      location_weight: 0.25,
      salary_weight: 0.30,
      role_weight: 0.25,
      company_weight: 0.20,
      auto_learn_from_swipes: true,
    };

    const { error: saveError } = await supabase
      .from('user_job_preferences')
      .upsert(testPreferences, { onConflict: 'user_id' });

    if (saveError) {
      console.error('❌ Preferences save failed:', saveError.message);
      return;
    }

    console.log('✅ Preferences saved successfully!');

    // Step 5: Mark onboarding as complete
    console.log('\n5️⃣ Marking onboarding as complete...');
    const { error: onboardingError } = await supabase
      .from('profiles')
      .update({ onboarding_completed: true })
      .eq('id', authData.user.id);

    if (onboardingError) {
      console.error('❌ Onboarding completion failed:', onboardingError.message);
      return;
    }

    console.log('✅ Onboarding marked as complete!');

    // Step 6: Verify final state
    console.log('\n6️⃣ Verifying final state...');
    const { data: finalProfile, error: finalError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', authData.user.id)
      .single();

    if (finalError) {
      console.error('❌ Final verification failed:', finalError.message);
      return;
    }

    console.log('✅ Final verification complete!');
    console.log('   Onboarding Completed:', finalProfile.onboarding_completed);

    console.log('\n🎉 Authentication flow test completed successfully!');
    console.log('\n📋 Summary:');
    console.log('   ✅ Login works');
    console.log('   ✅ Profile access works');
    console.log('   ✅ Preferences saving works');
    console.log('   ✅ Onboarding completion works');
    console.log('\n🚀 The user should now be able to access the main app!');

  } catch (error) {
    console.error('💥 Unexpected error:', error);
  }
}

// Run the test
testAuthFlow();