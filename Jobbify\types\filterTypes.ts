/**
 * Enhanced TypeScript interfaces for Filter Card UI system
 * Extends the existing UserJobPreferences with new fields for the card-based filter system
 */

// Enhanced location preferences
export interface LocationPreference {
  city: string;
  state?: string;
  country: string;
  priority: number; // 1-5 ranking
}

// Benefits importance rating
export interface BenefitsRating {
  health_insurance: number; // 1-5 importance
  paid_time_off: number;
  equity: number;
  retirement: number;
  professional_development: number;
}

// Job type enums
export type JobType = 'full-time' | 'part-time' | 'contract' | 'freelance' | 'internship';
export type ExperienceLevel = 'entry' | 'junior' | 'mid' | 'senior' | 'lead' | 'executive';
export type CompanySize = 'startup' | 'small' | 'medium' | 'large';
export type GrowthStage = 'early' | 'growth' | 'mature';
export type WorkArrangement = 'onsite' | 'remote' | 'hybrid' | 'flexible';
export type RemoteWorkPreference = 'required' | 'preferred' | 'acceptable' | 'not_preferred';

// Enhanced UserJobPreferences interface
export interface EnhancedUserJobPreferences {
  id?: string;
  user_id: string;
  
  // Enhanced location preferences
  preferred_locations: LocationPreference[];
  remote_work_preference: RemoteWorkPreference;
  max_commute_distance: number;
  willing_to_relocate: boolean;
  
  // Enhanced job type preferences
  preferred_job_types: JobType[];
  preferred_industries: string[];
  work_arrangement: WorkArrangement;
  
  // Enhanced compensation
  salary_range: {
    min: number;
    max: number;
    currency: string;
    negotiable: boolean;
  };
  benefits_importance: BenefitsRating;
  
  // Enhanced experience
  experience_level: ExperienceLevel;
  skills: string[];
  years_experience: number;
  preferred_roles: string[];
  
  // Enhanced company preferences
  preferred_company_sizes: CompanySize[];
  company_culture_preferences: string[];
  growth_stage_preferences: GrowthStage[];
  
  // Preference weights (for scoring algorithm)
  location_weight: number;
  salary_weight: number;
  role_weight: number;
  company_weight: number;
  
  // Learning preferences
  auto_learn_from_swipes: boolean;
  
  // Metadata
  setup_completed_at?: string;
  last_updated_at?: string;
}

// Filter card navigation types
export type FilterCardType = 
  | 'location' 
  | 'job-type' 
  | 'salary' 
  | 'experience' 
  | 'company-size';

export interface FilterCardProps {
  type: FilterCardType;
  preferences: EnhancedUserJobPreferences;
  onPreferencesChange: (preferences: Partial<EnhancedUserJobPreferences>) => void;
  onNext: () => void;
  onBack: () => void;
  onSkip: () => void;
  isFirst?: boolean;
  isLast?: boolean;
  currentStep: number;
  totalSteps: number;
  isValid?: boolean;
  isSaving?: boolean;
}

// Progress indicator types
export interface ProgressStep {
  id: FilterCardType;
  title: string;
  icon: string;
  completed: boolean;
  active: boolean;
}

export interface ProgressIndicatorProps {
  steps: ProgressStep[];
  currentStep: number;
  onStepPress: (stepIndex: number) => void;
}

// Filter onboarding flow types
export interface FilterOnboardingFlowProps {
  isOnboarding?: boolean;
  existingPreferences?: EnhancedUserJobPreferences;
  onComplete: (preferences: EnhancedUserJobPreferences) => void;
  onSkip?: () => void;
}

// Animation and interaction types
export interface FilterCardAnimationConfig {
  duration: number;
  easing: 'ease-in' | 'ease-out' | 'ease-in-out' | 'spring';
  delay?: number;
}

export interface FilterCardInteraction {
  type: 'selection' | 'input' | 'slider' | 'toggle';
  hapticFeedback?: boolean;
  animation?: FilterCardAnimationConfig;
}

// Default preferences factory
export const createDefaultEnhancedPreferences = (userId: string): EnhancedUserJobPreferences => ({
  user_id: userId,
  preferred_locations: [],
  remote_work_preference: 'preferred',
  max_commute_distance: 50,
  willing_to_relocate: false,
  preferred_job_types: ['full-time'],
  preferred_industries: [],
  work_arrangement: 'flexible',
  salary_range: {
    min: 50000,
    max: 100000,
    currency: 'USD',
    negotiable: true,
  },
  benefits_importance: {
    health_insurance: 4,
    paid_time_off: 3,
    equity: 2,
    retirement: 3,
    professional_development: 4,
  },
  experience_level: 'mid',
  skills: [],
  years_experience: 3,
  preferred_roles: [],
  preferred_company_sizes: ['small', 'medium'],
  company_culture_preferences: [],
  growth_stage_preferences: ['growth'],
  location_weight: 0.25,
  salary_weight: 0.30,
  role_weight: 0.25,
  company_weight: 0.20,
  auto_learn_from_swipes: true,
  setup_completed_at: new Date().toISOString(),
  last_updated_at: new Date().toISOString(),
});